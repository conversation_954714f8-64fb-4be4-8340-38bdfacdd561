* {
    font-family: monospace;
    /* transition: background-color 0.5s ease, color 0.5s ease !important; */
}

section {
    min-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.title {
    position: relative;
}

.title::after {
    content: "";
    height: 60%;
    width: 85%;
    display: block;
    position: absolute;
    bottom: -5px;
    right: -15px;
    z-index: -20;
}


/* Additional custom styles for enhanced visual effects */
.project-card-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tech-badge-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tech-badge-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
}

.button-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.image-overlay {
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

img {
    filter: invert(0) !important;
}